"""
Brewing Equipment Performance Analysis Report Parser

This parser handles brewing equipment reports with various formatting variations,
using Parmancer dataclass parsers to extract structured data.
"""

from dataclasses import dataclass
from typing import List, Optional, Union
import re
from pprint import pprint

from parmancer import (
    gather, regex, string, take, one_of, seq,
    ParseError, padding, whitespace
)


# Reusable parsers for common patterns
equals_line = regex(r"=+\n")  # Line of equals signs
any_line = regex(r"[^\n]*\n")  # Any line including newline
field_value = regex(r"([^\n]+)")  # Capture content until newline
number = regex(r"(\d+)").map(int)  # Integer number
number_with_commas = regex(r"([\d,]+)").map(lambda s: int(s.replace(',', '')))  # Number with commas


@dataclass
class ReportHeader:
    """Header information common to all brewing reports."""
    # Skip the initial equals line and title lines, then parse fields
    version: str = take(
        equals_line >>  # Skip first equals line
        any_line >>     # Skip title line
        regex(r"\s*Version\s+([^\s]+(?:\s+[^\s]+)*?)\s+-\s+Build[^\n]*\n", group=1)
    )

    report_id: str = take(
        equals_line >>  # Skip second equals line
        string("\n") >> # Skip empty line
        one_of(
            regex(r"Report\s+ID:\s*([^\n]+)\n", group=1),  # "Report ID:" with space
            regex(r"Report\s+Number:\s*([^\n]+)\n", group=1),  # "Report Number:" with space
            regex(r"ReportID:\s*([^\n]+)\n", group=1),  # "ReportID:" no space
            regex(r"Report_ID:\s*([^\n]+)\n", group=1)  # "Report_ID:" with underscore
        ).map(lambda s: s.strip())
    )

    generated_date: str = take(
        one_of(
            regex(r"Generated:\s*([^\n]+)\n", group=1),
            regex(r"Creation\s*Date:\s*([^\n]+)\n", group=1),
            regex(r"Timestamp:\s*([^\n]+)\n", group=1),
            regex(r"Date_Generated:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    operator: str = take(
        one_of(
            regex(r"Operator:\s*([^\n]+)\n", group=1),
            regex(r"Technician:\s*([^\n]+)\n", group=1),
            regex(r"User:\s*([^\n]+)\n", group=1),
            regex(r"Operator_Name:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    laboratory: str = take(
        one_of(
            regex(r"Laboratory:\s*([^\n]+)\n", group=1),
            regex(r"Facility:\s*([^\n]+)\n", group=1),
            regex(r"Lab:\s*([^\n]+)\n", group=1),
            regex(r"Laboratory_Location:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    equipment: str = take(
        one_of(
            regex(r"Equipment\s*Suite:\s*([^\n]+)\n", group=1),
            regex(r"Equipment:\s*([^\n]+)\n", group=1),
            regex(r"Equipment\s*Model:\s*([^\n]+)\n", group=1),
            regex(r"Equipment_Type:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    analysis_period: str = take(
        one_of(
            regex(r"Analysis\s*Period:\s*([^\n]+)\n", group=1),
            regex(r"Analysis\s*Window:\s*([^\n]+)\n", group=1),
            regex(r"Period:\s*([^\n]+)\n", group=1),
            regex(r"Analysis_Timeframe:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    total_batches: int = take(
        one_of(
            regex(r"Total\s*Batches:\s*(\d+)\n", group=1),
            regex(r"Batch\s*Count:\s*(\d+)\n", group=1),
            regex(r"Batch_Total:\s*(\d+)\n", group=1)
        ).map(int)
    )

    data_points: int = take(
        one_of(
            regex(r"Data\s*Points:\s*([\d,]+)\n", group=1),
            regex(r"Data\s*Records:\s*([\d,]+)\n", group=1),
            regex(r"Data_Count:\s*([\d,]+)\n", group=1)
        ).map(lambda s: int(s.replace(',', '')))
    )

    quality_threshold: str = take(
        one_of(
            regex(r"Quality\s*Threshold:\s*([^\n]+)\n", group=1),
            regex(r"QC\s*Threshold:\s*([^\n]+)\n", group=1),
            regex(r"Quality\s*Gate:\s*([^\n]+)\n", group=1),
            regex(r"Quality_Minimum:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    status: str = take(
        one_of(
            regex(r"Status:\s*([^\n]+)\n", group=1),
            regex(r"System\s*State:\s*([^\n]+)\n", group=1),
            regex(r"Operational_Status:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )


@dataclass
class BatchTemperatureData:
    """Temperature control data for individual batches."""
    batch_id: str = take(one_of(
        regex(r"(BT-\d+)"),
        regex(r"(Batch_\d+)"),
        regex(r"(B\d+)")
    ))
    target_temp: float = take(whitespace >> regex(r"[\d.]+").map(float))
    actual_temp: float = take(whitespace >> regex(r"[\d.]+").map(float))
    deviation: float = take(whitespace >> regex(r"[+-]?[\d.]+").map(float))
    hold_time: int = take(whitespace >> regex(r"\d+").map(int))
    efficiency: float = take(whitespace >> regex(r"[\d.]+").map(float) << string("\n"))


@dataclass
class MashTunSection:
    """Mash tun analysis section data."""
    temperature_batches: List[BatchTemperatureData] = take(
        # Handle optional blank line before section header - match by content, not number
        regex(r"\n?=+\n\s*SECTION\s+\d+[^\n]*MASH[^\n]*TUN[^\n]*\n=+\n\n") >>
        # Find the temperature control section - match exact patterns
        one_of(
            regex(r"Temperature Control Performance:\n"),
            regex(r"Temperature Control Results:\n"),
            regex(r"Temperature Performance Data:\n"),
            regex(r"Temperature Performance:\n")
        ) >>
        # Skip header lines
        regex(r"[^\n]*\n[^\n]*\n") >>
        # Parse batch data lines - they end with a blank line
        gather(BatchTemperatureData).many() << regex(r"\n") <<
        # Consume rest of section until next section header or end-of-file
        regex(r"(?:[^\n]*\n)*?(?==+\n\s*SECTION\s+\d+|$)")
    )


@dataclass
class VesselData:
    """Fermentation vessel performance data."""
    vessel_id: str = take(one_of(
        regex(r"FV-[A-Z]\d+"),
        regex(r"Tank_[A-Z]\d+"),
        regex(r"V\d+"),
        regex(r"T\d+")
    ))
    volume: int = take(whitespace >> regex(r"\d+").map(int))
    pressure: float = take(whitespace >> regex(r"[\d.]+").map(float))
    co2_rate: float = take(whitespace >> regex(r"[\d.]+").map(float))
    temperature: float = take(whitespace >> regex(r"[\d.]+").map(float))
    gravity: float = take(whitespace >> regex(r"[\d.]+").map(float))
    status: str = take(whitespace >> regex(r"\w+") << string("\n"))


@dataclass
class YeastSample:
    """Yeast viability assessment data."""
    sample_id: str = take(regex(r"YS-\d+"))
    cell_count: str = take(whitespace >> regex(r"[\d.]+e\d+"))
    viability: float = take(whitespace >> regex(r"[\d.]+").map(float))
    budding: float = take(whitespace >> regex(r"[\d.]+").map(float))
    contamination: str = take(whitespace >> regex(r"[<\d.%]+"))
    grade: str = take(whitespace >> regex(r"[A-Z][+]?") << string("\n"))


@dataclass
class FermentationSection:
    """Fermentation vessel and yeast analysis data."""
    vessels: List[VesselData] = take(
        # Handle optional blank line before section header - match by content, not number
        regex(r"\n?=+\n\s*SECTION\s+\d+[^\n]*FERMENTATION[^\n]*\n=+\n\n") >>
        # Find vessel section - use a single pattern that matches all variations
        regex(r"[^\n]*(?:Vessel|Tank)[^\n]*(?:Status|Monitoring|Performance|Data)[^\n]*:\n") >>
        # Skip header
        regex(r"[^\n]*\n[^\n]*\n") >>
        # Parse vessel data
        gather(VesselData).many() <<
        # Consume rest of vessel section until yeast section
        regex(r"(?:[^\n]*\n)*?(?=[^\n]*(?:Yeast|Microorganism|Culture)[^\n]*(?:Analysis|Assessment|Viability)[^\n]*:\n)")
    )
    yeast_samples: List[YeastSample] = take(
        # Skip any blank lines before yeast section
        regex(r"\n*") >>
        # Find yeast section - use a single pattern that matches all variations
        regex(r"[^\n]*(?:Yeast|Microorganism|Culture)[^\n]*(?:Analysis|Assessment|Viability)[^\n]*:\n") >>
        # Skip header
        regex(r"[^\n]*\n[^\n]*\n") >>
        # Parse yeast data
        gather(YeastSample).many() <<
        # Consume rest of section until next section header or end-of-file
        regex(r"(?:[^\n]*\n)*?(?==+\n\s*SECTION\s+\d+|$)")
    )


@dataclass
class FilterStage:
    """Individual filtration stage data."""
    stage: int = take(whitespace >> regex(r"(\d+)").map(int))
    filter_type: str = take(whitespace >> regex(r"(\w+)"))
    pore_size: str = take(whitespace >> regex(r"([\d.]+(?:μm)?)"))
    flow_rate: str = take(whitespace >> regex(r"([\d.]+(?:\s*L/h)?)"))
    pressure_drop: str = take(whitespace >> regex(r"([\d.]+(?:\s*PSI)?)"))
    efficiency: str = take(whitespace >> regex(r"([\d.]+%?)"))
    hours: int = take(whitespace >> regex(r"(\d+)").map(int))


@dataclass
class FiltrationSection:
    """Filtration system performance data."""
    filter_stages: List[FilterStage] = take(
        # Handle optional blank line before section header - match by content, not number
        regex(r"\n?=+\n\s*SECTION\s+\d+[^\n]*FILTRATION[^\n]*\n=+\n\n") >>
        # Find the actual data table header
        regex(r"[^\n]*(?:Matrix|Bank|Status)[^\n]*:\n") >>
        # Skip header lines
        regex(r"[^\n]*\n[^\n]*\n") >>
        # Parse filter stage data separated by newlines
        gather(FilterStage).sep_by(regex(r"\n")) << regex(r"\n\n")
    )
    total_efficiency: Optional[str] = take(
        regex(r"Total System Efficiency:\s*([^\n]+)\n", group=1).optional()
    )
    replacement_recommendation: Optional[str] = take(
        regex(r"Recommended Filter Replacement:\s*([^\n]+)\n", group=1).optional() <<
        # Consume rest of section until next section header or end-of-file
        regex(r"(?:[^\n]*\n)*?(?==+\n\s*SECTION\s+\d+|$)")
    )


@dataclass
class QualityMetric:
    """Individual quality measurement."""
    parameter: str = take(regex(r"([A-Za-z][A-Za-z\s_]+[A-Za-z])"))  # Handle spaces and underscores in parameter names
    target_range: str = take(regex(r"\s+([^\s]+(?:\s[^\s]+)*)"))
    measured_value: str = take(regex(r"\s+([^\s]+(?:\s[^\s]+)*)"))
    deviation: str = take(regex(r"\s+([+-]?[\d.]+%?)"))
    status: str = take(regex(r"\s+(\w+)") << string("\n"))


@dataclass
class MicrobiologicalTest:
    """Microbiological test result."""
    test_type: str = take(regex(r"([A-Za-z](?:[A-Za-z_]|\s[A-Za-z])*[A-Za-z])(?=\s{2,})"))  # Handle single spaces and underscores, stop at 2+ spaces
    result: str = take(regex(r"\s+([^\s]+(?:\s[^\s]+)*)(?=\s{2,})").map(str.strip))  # Match and trim
    specification: str = take(regex(r"\s{2,}([^\s]+(?:\s[^\s]+)*)(?=\s{2,})").map(str.strip))  # Match and trim
    status: str = take(regex(r"\s{2,}([^\s]+(?:\s[^\s]+)*)(?=\n)").map(str.strip) << string("\n"))  # Match until newline and trim


@dataclass
class QualitySection:
    """Quality metrics and microbiological analysis."""
    quality_metrics: List[QualityMetric] = take(
        # Handle optional blank line before section header - match by content, not number
        regex(r"\n?=+\n\s*SECTION\s+\d+[^\n]*(?:QUALITY|CONTROL|ASSURANCE)[^\n]*\n=+\n\n") >>
        # Find the actual data table header
        regex(r"[^\n]*(?:Results|Analysis|Summary|Metrics)[^\n]*:\n") >>
        # Skip header lines
        regex(r"[^\n]*\n[^\n]*\n") >>
        # Parse quality metrics
        gather(QualityMetric).many() <<
        # Consume rest of section until microbiological section or end-of-file
        regex(r"(?:[^\n]*\n)*?(?=[^\n]*(?:Microbiological|Microorganism)[^\n]*:\n|$)")
    )
    microbiological_tests: List[MicrobiologicalTest] = take(
        (
            # Find microbiological section
            regex(r"[^\n]*(?:Microbiological|Microorganism|Contamination)[^\n]*:\n") >>
            # Skip header lines
            regex(r"[^\n]*\n[^\n]*\n") >>
            # Parse microbiological tests
            gather(MicrobiologicalTest).many() <<
            # Consume rest of section until end-of-file
            regex(r"(?:[^\n]*\n)*?$")
        ).optional().map(lambda x: x if x is not None else [])
    )


# Create a parser that can match any section
def section_parser():
    """Parser that can identify and parse any section."""
    return one_of(
        gather(MashTunSection).map(lambda x: ("mash_tun", x)),
        gather(FermentationSection).map(lambda x: ("fermentation", x)),
        gather(FiltrationSection).map(lambda x: ("filtration", x)),
        gather(QualitySection).map(lambda x: ("quality", x))
    )

@dataclass
class BrewingReport:
    """Complete brewing equipment performance analysis report."""
    header: ReportHeader = take(gather(ReportHeader))
    mash_tun: Optional[MashTunSection] = None
    fermentation: Optional[FermentationSection] = None
    filtration: Optional[FiltrationSection] = None
    quality: Optional[QualitySection] = None
    remaining_content: str = ""


def create_brewing_report_parser():
    """Create the main brewing report parser."""
    # Parse header first
    header_parser = gather(ReportHeader)

    # Parse sections in any order
    sections_parser = section_parser().many()

    # Parse any remaining content
    remaining_parser = regex(r"[\s\S]*")

    # Combine header, sections, and remaining content
    def combine_results(parts):
        """Combine header, sections, and remaining content into a BrewingReport."""
        header, sections_list, remaining = parts
        report = BrewingReport(header=header, remaining_content=remaining)

        # Assign sections to appropriate fields
        for section_type, section_data in sections_list:
            if section_type == "mash_tun":
                report.mash_tun = section_data
            elif section_type == "fermentation":
                report.fermentation = section_data
            elif section_type == "filtration":
                report.filtration = section_data
            elif section_type == "quality":
                report.quality = section_data

        return report

    return seq(header_parser, sections_parser, remaining_parser).map(combine_results)


def parse_brewing_report(file_path: str, debug: bool = False) -> Union[BrewingReport, None]:
    """
    Parse a brewing report file and return the structured data.

    Args:
        file_path: Path to the brewing report file
        debug: Enable debug mode for detailed error information

    Returns:
        BrewingReport object if successful, None if parsing fails
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        parser = create_brewing_report_parser()
        result = parser.parse(content, debug=debug)
        return result

    except ParseError as e:
        print(f"Parse error in {file_path}:")
        print(str(e))
        return None
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None


def test_all_sample_files():
    """Test the parser on all sample files."""
    import os

    sample_dir = "examples/sample_data"
    sample_files = [f for f in os.listdir(sample_dir) if f.endswith('.txt')]
    sample_files.sort()

    results = []

    for filename in sample_files:
        file_path = os.path.join(sample_dir, filename)
        result = parse_brewing_report(file_path, debug=False)
        pprint(result)
        # break
        if result:
            results.append({
                'file': filename,
                'report_id': result.header.report_id,
                'operator': result.header.operator,
                'total_batches': result.header.total_batches,
                'status': result.header.status,
                'temp_batches_count': len(result.mash_tun.temperature_batches) if result.mash_tun else 0,
                'vessels_count': len(result.fermentation.vessels) if result.fermentation else 0,
                'yeast_samples_count': len(result.fermentation.yeast_samples) if result.fermentation else 0,
                'filter_stages_count': len(result.filtration.filter_stages) if result.filtration else 0,
                'quality_metrics_count': len(result.quality.quality_metrics) if result.quality else 0,
                'micro_tests_count': len(result.quality.microbiological_tests) if result.quality else 0
            })
        else:
            print(f"Failed to parse {filename}")
            parse_brewing_report(file_path, debug=True)

    if results:
        # Print as table
        headers = ['File', 'Report ID', 'Operator', 'Batches', 'Status', 'Temp', 'Vessels', 'Yeast', 'Filters', 'Quality', 'Micro']
        print(f"{headers[0]:<8} {headers[1]:<20} {headers[2]:<15} {headers[3]:<8} {headers[4]:<12} {headers[5]:<6} {headers[6]:<8} {headers[7]:<6} {headers[8]:<8} {headers[9]:<8} {headers[10]:<6}")
        print("-" * 110)

        for r in results:
            print(f"{r['file']:<8} {r['report_id']:<20} {r['operator']:<15} {r['total_batches']:<8} "
                  f"{r['status']:<12} {r['temp_batches_count']:<6} {r['vessels_count']:<8} {r['yeast_samples_count']:<6} "
                  f"{r['filter_stages_count']:<8} {r['quality_metrics_count']:<8} {r['micro_tests_count']:<6}")

        return results
    return None


if __name__ == "__main__":
    test_all_sample_files()
